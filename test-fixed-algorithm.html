<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的字符串艺术算法测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .input-section,
        .output-section {
            flex: 1;
            min-width: 300px;
        }

        canvas {
            border: 1px solid #ccc;
            max-width: 100%;
        }

        #outputCanvas {
            /* 输出画布实际大小是 IMG_SIZE * 2，但显示时缩放到合适大小 */
            max-width: 500px;
            max-height: 500px;
        }

        .controls {
            margin: 20px 0;
        }

        .controls label {
            display: block;
            margin: 10px 0 5px 0;
        }

        .controls input,
        .controls button {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
        }

        .progress {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }

        .hidden {
            display: none;
        }

        #status {
            font-weight: bold;
            color: #333;
        }
    </style>
</head>

<body>
    <h1>修复后的字符串艺术算法测试</h1>

    <div class="container">
        <div class="input-section">
            <h2>输入</h2>
            <input type="file" id="imageInput" accept="image/*">
            <canvas id="inputCanvas" width="500" height="500"></canvas>

            <div class="controls">
                <label for="imageSize">图像大小:</label>
                <input type="number" id="imageSize" value="500" min="100" max="1000">

                <label for="nPins">Number of Pins:</label>
                <input type="number" id="nPins" value="288" min="50" max="500">

                <label for="maxLines">Number of Lines:</label>
                <input type="number" id="maxLines" value="4000" min="100" max="10000">

                <label for="lineWeight">Line Weight:</label>
                <input type="number" id="lineWeight" value="20" min="1" max="100">

                <button id="generateBtn">生成字符串艺术</button>
            </div>

            <div class="progress">
                <div id="status">等待输入图像...</div>
                <div id="progressBar" style="width: 0%; height: 20px; background: #4CAF50; transition: width 0.3s;">
                </div>
            </div>
        </div>

        <div class="output-section">
            <h2>输出</h2>
            <canvas id="outputCanvas" width="500" height="500"></canvas>

            <div id="results" class="hidden">
                <h3>结果信息</h3>
                <p>线条数量: <span id="lineCount">-</span></p>
                <p>线程长度: <span id="threadLength">-</span> 米</p>
                <p>处理时间: <span id="processingTime">-</span> 秒</p>

                <h3>钉子序列</h3>
                <textarea id="pinSequence" rows="10" style="width: 100%; font-family: monospace;"></textarea>
            </div>
        </div>
    </div>

    <script type="module">
        import OriginalStringArtAlgorithm from './src/modules/originalAlgorithm.js';

        const imageInput = document.getElementById('imageInput');
        const inputCanvas = document.getElementById('inputCanvas');
        const outputCanvas = document.getElementById('outputCanvas');
        const generateBtn = document.getElementById('generateBtn');
        const status = document.getElementById('status');
        const progressBar = document.getElementById('progressBar');
        const results = document.getElementById('results');

        const inputCtx = inputCanvas.getContext('2d');
        const outputCtx = outputCanvas.getContext('2d');

        let currentImage = null;
        let algorithm = null;

        // 处理图像上传
        imageInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    // 绘制图像到输入画布
                    const size = parseInt(document.getElementById('imageSize').value);
                    inputCanvas.width = size;
                    inputCanvas.height = size;

                    // 计算居中裁剪
                    const scale = Math.min(img.width, img.height);
                    const x = (img.width - scale) / 2;
                    const y = (img.height - scale) / 2;

                    inputCtx.drawImage(img, x, y, scale, scale, 0, 0, size, size);

                    // 应用圆形遮罩
                    inputCtx.globalCompositeOperation = 'destination-in';
                    inputCtx.beginPath();
                    inputCtx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
                    inputCtx.closePath();
                    inputCtx.fill();
                    inputCtx.globalCompositeOperation = 'source-over';

                    currentImage = inputCtx.getImageData(0, 0, size, size);
                    status.textContent = '图像已加载，可以开始生成';
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        });

        // 生成按钮点击事件
        generateBtn.addEventListener('click', async () => {
            if (!currentImage) {
                alert('请先选择一张图像');
                return;
            }

            generateBtn.disabled = true;
            results.classList.add('hidden');

            const startTime = Date.now();

            try {
                // 创建算法实例
                algorithm = new OriginalStringArtAlgorithm({
                    imageSize: parseInt(document.getElementById('imageSize').value),
                    nPins: parseInt(document.getElementById('nPins').value),
                    maxLines: parseInt(document.getElementById('maxLines').value),
                    lineWeight: parseInt(document.getElementById('lineWeight').value),
                    minDistance: 20,
                    hoopDiameter: 0.625,
                    scale: 20
                });

                // 生成字符串艺术
                const result = await algorithm.generate(currentImage, (progress) => {
                    status.textContent = getStatusText(progress);
                    progressBar.style.width = (progress.progress * 100) + '%';

                    // 如果是绘制线条阶段，实时更新输出画布
                    if (progress.step === 'drawing-lines' && progress.linesDrawn % 100 === 0) {
                        drawCurrentProgress();
                    }
                });

                // 绘制最终结果
                drawResult(result);

                // 显示结果信息
                const endTime = Date.now();
                const processingTime = (endTime - startTime) / 1000;

                document.getElementById('lineCount').textContent = result.lineSequence.length;
                document.getElementById('threadLength').textContent = result.threadLength.toFixed(2);
                document.getElementById('processingTime').textContent = processingTime.toFixed(1);
                document.getElementById('pinSequence').value = result.lineSequence.join(',');

                results.classList.remove('hidden');
                status.textContent = '生成完成！';

            } catch (error) {
                console.error('生成失败:', error);
                status.textContent = '生成失败: ' + error.message;
            } finally {
                generateBtn.disabled = false;
                progressBar.style.width = '0%';
            }
        });

        function getStatusText(progress) {
            switch (progress.step) {
                case 'processing-image':
                    return '处理图像中...';
                case 'calculating-pins':
                    return '计算钉子位置...';
                case 'precalculating-lines':
                    return '预计算线条...';
                case 'drawing-lines':
                    return `绘制线条中... ${progress.linesDrawn}/${progress.totalLines}`;
                case 'complete':
                    return '生成完成！';
                default:
                    return '处理中...';
            }
        }

        function drawCurrentProgress() {
            if (!algorithm || !algorithm.line_sequence || !algorithm.pin_coords) return;

            const size = parseInt(document.getElementById('imageSize').value);
            // 与原版本一致：画布大小为 IMG_SIZE * 2
            outputCanvas.width = size * 2;
            outputCanvas.height = size * 2;

            // 清空画布（白色背景）
            outputCtx.fillStyle = 'white';
            outputCtx.fillRect(0, 0, size * 2, size * 2);

            // 与原版本完全一致的绘制参数
            outputCtx.strokeStyle = 'black';
            outputCtx.lineWidth = 0.3;  // 与原版本一致
            outputCtx.globalAlpha = 1.0;  // 与原版本一致，不使用透明度

            for (let i = 0; i < algorithm.line_sequence.length - 1; i++) {
                const pin1 = algorithm.pin_coords[algorithm.line_sequence[i]];
                const pin2 = algorithm.pin_coords[algorithm.line_sequence[i + 1]];

                outputCtx.beginPath();
                // 与原版本一致：坐标乘以2
                outputCtx.moveTo(pin1[0] * 2, pin1[1] * 2);
                outputCtx.lineTo(pin2[0] * 2, pin2[1] * 2);
                outputCtx.stroke();
            }
        }

        function drawResult(result) {
            if (!result || !result.lineSequence || !result.pinCoords) return;

            const size = parseInt(document.getElementById('imageSize').value);
            // 与原版本一致：画布大小为 IMG_SIZE * 2
            outputCanvas.width = size * 2;
            outputCanvas.height = size * 2;

            // 清空画布（白色背景）
            outputCtx.fillStyle = 'white';
            outputCtx.fillRect(0, 0, size * 2, size * 2);

            // 与原版本完全一致的绘制参数
            outputCtx.strokeStyle = 'black';
            outputCtx.lineWidth = 0.3;  // 与原版本一致
            outputCtx.globalAlpha = 1.0;  // 与原版本一致，不使用透明度

            for (let i = 0; i < result.lineSequence.length - 1; i++) {
                const pin1 = result.pinCoords[result.lineSequence[i]];
                const pin2 = result.pinCoords[result.lineSequence[i + 1]];

                outputCtx.beginPath();
                // 与原版本一致：坐标乘以2
                outputCtx.moveTo(pin1[0] * 2, pin1[1] * 2);
                outputCtx.lineTo(pin2[0] * 2, pin2[1] * 2);
                outputCtx.stroke();
            }
        }
    </script>
</body>

</html>