<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick String Art Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #1976d2; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        canvas { border: 1px solid #ccc; margin: 10px 0; display: block; }
        .progress { width: 100%; height: 20px; background: #e0e0e0; margin: 10px 0; }
        .progress-bar { height: 100%; background: #4caf50; width: 0%; transition: width 0.3s; }
        .info { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Quick String Art Test</h1>
    
    <div>
        <button onclick="runQuickTest()">Run Quick Test</button>
        <button onclick="clearCanvas()">Clear</button>
    </div>
    
    <div class="progress">
        <div class="progress-bar" id="progress"></div>
    </div>
    
    <div id="info" class="info">Click "Run Quick Test" to start</div>
    
    <canvas id="canvas" width="600" height="600"></canvas>

    <script type="module">
        // 模拟 StringArtGenerator 的核心功能
        class QuickStringArtTest {
            constructor() {
                this.canvas = document.getElementById('canvas');
                this.ctx = this.canvas.getContext('2d');
                this.isRunning = false;
            }

            async runTest() {
                if (this.isRunning) return;
                this.isRunning = true;

                try {
                    this.updateInfo('Starting test...');
                    
                    // 1. 创建测试图像
                    this.updateInfo('Creating test image...');
                    const imageData = this.createTestImage();
                    this.updateProgress(0.1);

                    // 2. 计算钉子位置
                    this.updateInfo('Calculating pin positions...');
                    const pins = this.calculatePins(288, 300);
                    this.updateProgress(0.2);

                    // 3. 预计算线条
                    this.updateInfo('Precalculating lines...');
                    const lineCache = this.precalculateLines(pins);
                    this.updateProgress(0.3);

                    // 4. 生成String Art
                    this.updateInfo('Generating string art...');
                    await this.generateStringArt(imageData, pins, lineCache);
                    
                    this.updateInfo('Test completed successfully!');
                    this.updateProgress(1);
                } catch (error) {
                    this.updateInfo('Test failed: ' + error.message);
                } finally {
                    this.isRunning = false;
                }
            }

            createTestImage() {
                const size = 300;
                const imageData = new Array(size * size);
                
                // 创建简单的测试图案
                for (let y = 0; y < size; y++) {
                    for (let x = 0; x < size; x++) {
                        const dx = x - size/2;
                        const dy = y - size/2;
                        const distance = Math.sqrt(dx*dx + dy*dy);
                        
                        let value = 255; // 白色背景
                        
                        // 添加一些图案
                        if (distance < size/4) {
                            value = 0; // 黑色圆圈
                        }
                        if (distance < size/8) {
                            value = 128; // 灰色内圈
                        }
                        
                        // 添加眼睛
                        const eye1dx = x - (size/2 - size/12);
                        const eye1dy = y - (size/2 - size/12);
                        const eye2dx = x - (size/2 + size/12);
                        const eye2dy = y - (size/2 - size/12);
                        
                        if (Math.sqrt(eye1dx*eye1dx + eye1dy*eye1dy) < size/24 ||
                            Math.sqrt(eye2dx*eye2dx + eye2dy*eye2dy) < size/24) {
                            value = 255; // 白色眼睛
                        }
                        
                        imageData[y * size + x] = value;
                    }
                }
                
                return imageData;
            }

            calculatePins(numberOfPins, radius) {
                const pins = [];
                const center = 300;
                
                for (let i = 0; i < numberOfPins; i++) {
                    const angle = 2 * Math.PI * i / numberOfPins;
                    const x = center + radius * Math.cos(angle);
                    const y = center + radius * Math.sin(angle);
                    pins.push([x, y]);
                }
                
                return pins;
            }

            precalculateLines(pins) {
                const cache = {
                    coords: new Map(),
                    lengths: new Map()
                };
                
                for (let i = 0; i < pins.length; i++) {
                    for (let j = i + 20; j < pins.length; j++) {
                        const key = `${i}-${j}`;
                        const coords = this.calculateLineCoords(pins[i], pins[j]);
                        cache.coords.set(key, coords);
                        cache.lengths.set(key, coords.length);
                    }
                }
                
                return cache;
            }

            calculateLineCoords(pin1, pin2) {
                const [x0, y0] = pin1;
                const [x1, y1] = pin2;
                const distance = Math.floor(Math.sqrt((x1-x0)*(x1-x0) + (y1-y0)*(y1-y0)));
                
                const coords = [];
                for (let i = 0; i < distance; i++) {
                    const t = i / distance;
                    const x = Math.floor(x0 + t * (x1 - x0));
                    const y = Math.floor(y0 + t * (y1 - y0));
                    coords.push([x, y]);
                }
                
                return coords;
            }

            async generateStringArt(imageData, pins, lineCache) {
                // 清空画布
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, 600, 600);
                
                // 创建误差数组
                const errorArray = imageData.map(pixel => 255 - pixel);
                
                // 生成线条序列
                const sequence = [0];
                let currentPin = 0;
                const maxLines = 1000; // 减少线条数以便快速测试
                const recentPins = [];
                
                this.ctx.strokeStyle = 'black';
                this.ctx.lineWidth = 0.5;
                
                for (let lineIndex = 0; lineIndex < maxLines; lineIndex++) {
                    // 寻找最佳下一个钉子
                    let bestPin = -1;
                    let maxError = -1;
                    
                    for (let offset = 20; offset < pins.length - 20; offset++) {
                        const testPin = (currentPin + offset) % pins.length;
                        
                        if (recentPins.includes(testPin)) continue;
                        
                        const key = `${Math.min(currentPin, testPin)}-${Math.max(currentPin, testPin)}`;
                        const coords = lineCache.coords.get(key);
                        
                        if (coords) {
                            const lineError = this.calculateLineError(errorArray, coords);
                            if (lineError > maxError) {
                                maxError = lineError;
                                bestPin = testPin;
                            }
                        }
                    }
                    
                    if (bestPin === -1) break;
                    
                    // 绘制线条
                    const [x0, y0] = pins[currentPin];
                    const [x1, y1] = pins[bestPin];
                    
                    this.ctx.beginPath();
                    this.ctx.moveTo(x0, y0);
                    this.ctx.lineTo(x1, y1);
                    this.ctx.stroke();
                    
                    // 更新误差数组
                    const key = `${Math.min(currentPin, bestPin)}-${Math.max(currentPin, bestPin)}`;
                    const coords = lineCache.coords.get(key);
                    this.updateErrorArray(errorArray, coords, 20);
                    
                    // 更新状态
                    sequence.push(bestPin);
                    recentPins.push(bestPin);
                    if (recentPins.length > 20) recentPins.shift();
                    currentPin = bestPin;
                    
                    // 更新进度
                    const progress = 0.3 + (lineIndex / maxLines) * 0.7;
                    this.updateProgress(progress);
                    this.updateInfo(`Generated ${lineIndex + 1}/${maxLines} lines`);
                    
                    // 每10条线暂停一下
                    if (lineIndex % 10 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }
                
                return sequence;
            }

            calculateLineError(errorArray, coords) {
                let totalError = 0;
                for (const [x, y] of coords) {
                    const index = Math.floor(y) * 300 + Math.floor(x);
                    if (index >= 0 && index < errorArray.length) {
                        totalError += errorArray[index];
                    }
                }
                return totalError;
            }

            updateErrorArray(errorArray, coords, weight) {
                for (const [x, y] of coords) {
                    const index = Math.floor(y) * 300 + Math.floor(x);
                    if (index >= 0 && index < errorArray.length) {
                        errorArray[index] = Math.max(0, errorArray[index] - weight);
                    }
                }
            }

            updateProgress(progress) {
                document.getElementById('progress').style.width = (progress * 100) + '%';
            }

            updateInfo(message) {
                document.getElementById('info').textContent = message;
            }

            clear() {
                this.ctx.fillStyle = 'white';
                this.ctx.fillRect(0, 0, 600, 600);
                this.updateProgress(0);
                this.updateInfo('Canvas cleared');
            }
        }

        // 创建全局实例
        window.stringArtTest = new QuickStringArtTest();
        
        // 全局函数
        window.runQuickTest = () => window.stringArtTest.runTest();
        window.clearCanvas = () => window.stringArtTest.clear();
    </script>
</body>
</html>
