// pages/string-art.js
import { useState, useRef, useCallback } from 'react';
import StringArtGenerator from '../src/modules/stringArtGenerator.js';

// React Hook for String Art Generator
function useStringArtGenerator(config = {}) {
  const [generator] = useState(() => new StringArtGenerator(config));
  const [state, setState] = useState(generator.getState());
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const updateState = useCallback(() => {
    setState(generator.getState());
  }, [generator]);

  const processImage = useCallback(async (imageInput, canvas) => {
    try {
      setError(null);
      updateState();
      
      const result = await generator.processImage(imageInput, canvas);
      updateState();
      return result;
    } catch (err) {
      setError(err.message);
      updateState();
      throw err;
    }
  }, [generator, updateState]);

  const generateStringArt = useCallback(async (options = {}) => {
    try {
      setError(null);
      updateState();

      const result = await generator.generateStringArt({
        ...options,
        onProgress: (progress) => {
          updateState();
          options.onProgress?.(progress);
        }
      });

      setResult(result);
      updateState();
      return result;
    } catch (err) {
      setError(err.message);
      updateState();
      throw err;
    }
  }, [generator, updateState]);

  const reset = useCallback(() => {
    generator.reset();
    setResult(null);
    setError(null);
    updateState();
  }, [generator, updateState]);

  return {
    generator,
    state,
    result,
    error,
    processImage,
    generateStringArt,
    reset
  };
}

// Main Component
export default function StringArtPage() {
  const canvasRef = useRef(null);
  const previewCanvasRef = useRef(null);
  const fileInputRef = useRef(null);
  
  const [config, setConfig] = useState({
    imageSize: 500,
    numberOfPins: 288,
    maxLines: 2000,  // 减少线条数以便快速测试
    lineWeight: 20,
    minDistance: 20
  });

  const {
    generator,
    state,
    result,
    error,
    processImage,
    generateStringArt,
    reset
  } = useStringArtGenerator(config);

  const [progress, setProgress] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      await processImage(file, previewCanvasRef.current);
    } catch (err) {
      console.error('Failed to process image:', err);
    }
  };

  const handleGenerate = async () => {
    if (!state.hasProcessedImage) {
      alert('Please select an image first');
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    try {
      await generateStringArt({
        canvas: canvasRef.current,
        onProgress: (progressData) => {
          setProgress(progressData.progress);
        }
      });
    } catch (err) {
      console.error('Failed to generate string art:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleConfigChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDownload = () => {
    if (canvasRef.current) {
      generator.renderer.downloadImage(canvasRef.current, 'string-art');
    }
  };

  const handleExportSequence = () => {
    if (result) {
      const sequence = generator.exportLineSequence();
      const blob = new Blob([sequence], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'string-art-sequence.txt';
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  // 创建测试图像
  const createTestImage = () => {
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 200;
    const ctx = canvas.getContext('2d');
    
    // 绘制简单的测试图案
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 200, 200);
    
    ctx.fillStyle = 'black';
    ctx.beginPath();
    ctx.arc(100, 100, 60, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.fillStyle = 'white';
    ctx.beginPath();
    ctx.arc(80, 80, 15, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.beginPath();
    ctx.arc(120, 80, 15, 0, Math.PI * 2);
    ctx.fill();
    
    // 转换为Blob
    canvas.toBlob(async (blob) => {
      try {
        await processImage(blob, previewCanvasRef.current);
      } catch (err) {
        console.error('Failed to process test image:', err);
      }
    });
  };

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '20px' }}>
      <h1>String Art Generator - Next.js Demo</h1>
      
      {/* 配置面板 */}
      <div style={{ background: '#f5f5f5', padding: '20px', borderRadius: '8px', marginBottom: '20px' }}>
        <h3>Configuration</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <label style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            Number of Pins:
            <input
              type="number"
              value={config.numberOfPins}
              onChange={(e) => handleConfigChange('numberOfPins', parseInt(e.target.value))}
              min="50"
              max="500"
              style={{ padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
            />
          </label>
          
          <label style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            Max Lines:
            <input
              type="number"
              value={config.maxLines}
              onChange={(e) => handleConfigChange('maxLines', parseInt(e.target.value))}
              min="100"
              max="10000"
              style={{ padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
            />
          </label>
          
          <label style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            Line Weight:
            <input
              type="number"
              value={config.lineWeight}
              onChange={(e) => handleConfigChange('lineWeight', parseInt(e.target.value))}
              min="1"
              max="50"
              style={{ padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
            />
          </label>
          
          <label style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
            Min Distance:
            <input
              type="number"
              value={config.minDistance}
              onChange={(e) => handleConfigChange('minDistance', parseInt(e.target.value))}
              min="5"
              max="50"
              style={{ padding: '8px', border: '1px solid #ddd', borderRadius: '4px' }}
            />
          </label>
        </div>
      </div>

      {/* 文件选择 */}
      <div style={{ marginBottom: '20px' }}>
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />
        <button 
          onClick={() => fileInputRef.current?.click()}
          style={{ 
            background: '#2196f3', 
            color: 'white', 
            border: 'none', 
            padding: '10px 20px', 
            borderRadius: '4px', 
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Select Image
        </button>
        <button 
          onClick={createTestImage}
          style={{ 
            background: '#4caf50', 
            color: 'white', 
            border: 'none', 
            padding: '10px 20px', 
            borderRadius: '4px', 
            cursor: 'pointer'
          }}
        >
          Use Test Image
        </button>
      </div>

      {/* 预览画布 */}
      {state.hasProcessedImage && (
        <div style={{ marginBottom: '20px' }}>
          <h3>Processed Image</h3>
          <canvas
            ref={previewCanvasRef}
            style={{ border: '1px solid #ccc', maxWidth: '400px', display: 'block' }}
          />
        </div>
      )}

      {/* 生成按钮和进度 */}
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={handleGenerate}
          disabled={!state.hasProcessedImage || isGenerating}
          style={{ 
            background: (!state.hasProcessedImage || isGenerating) ? '#ccc' : '#2196f3',
            color: 'white', 
            border: 'none', 
            padding: '10px 20px', 
            borderRadius: '4px', 
            cursor: (!state.hasProcessedImage || isGenerating) ? 'not-allowed' : 'pointer',
            marginRight: '10px'
          }}
        >
          {isGenerating ? 'Generating...' : 'Generate String Art'}
        </button>
        
        <button
          onClick={reset}
          style={{ 
            background: '#f44336', 
            color: 'white', 
            border: 'none', 
            padding: '10px 20px', 
            borderRadius: '4px', 
            cursor: 'pointer'
          }}
        >
          Reset
        </button>
        
        {isGenerating && (
          <div style={{ 
            position: 'relative',
            width: '100%',
            height: '20px',
            background: '#e0e0e0',
            borderRadius: '10px',
            marginTop: '10px',
            overflow: 'hidden'
          }}>
            <div
              style={{ 
                height: '100%',
                background: '#4caf50',
                width: `${progress * 100}%`,
                transition: 'width 0.3s ease'
              }}
            />
            <span style={{ 
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              fontSize: '12px',
              fontWeight: 'bold'
            }}>
              {Math.round(progress * 100)}%
            </span>
          </div>
        )}
      </div>

      {/* 结果画布 */}
      {result && (
        <div style={{ marginBottom: '20px' }}>
          <h3>String Art Result</h3>
          <canvas
            ref={canvasRef}
            style={{ border: '1px solid #ccc', maxWidth: '800px', display: 'block' }}
          />
          
          <div style={{ margin: '10px 0' }}>
            <p>Total Lines: {result.totalLines}</p>
            <p>Thread Length: {result.threadLength.toFixed(2)} units</p>
          </div>
          
          <div style={{ display: 'flex', gap: '10px' }}>
            <button 
              onClick={handleDownload}
              style={{ 
                background: '#2196f3', 
                color: 'white', 
                border: 'none', 
                padding: '10px 20px', 
                borderRadius: '4px', 
                cursor: 'pointer'
              }}
            >
              Download Image
            </button>
            <button 
              onClick={handleExportSequence}
              style={{ 
                background: '#ff9800', 
                color: 'white', 
                border: 'none', 
                padding: '10px 20px', 
                borderRadius: '4px', 
                cursor: 'pointer'
              }}
            >
              Export Sequence
            </button>
          </div>
        </div>
      )}

      {/* 错误显示 */}
      {error && (
        <div style={{ 
          background: '#ffebee',
          color: '#c62828',
          padding: '10px',
          borderRadius: '4px',
          marginBottom: '20px'
        }}>
          Error: {error}
        </div>
      )}

      {/* 状态信息 */}
      <div style={{ 
        background: '#f5f5f5',
        padding: '10px',
        borderRadius: '4px',
        fontSize: '14px'
      }}>
        <h4>Status:</h4>
        <p>Processing: {state.isProcessing ? 'Yes' : 'No'}</p>
        <p>Current Step: {state.currentStep}</p>
        <p>Has Processed Image: {state.hasProcessedImage ? 'Yes' : 'No'}</p>
        <p>Has Line Sequence: {state.hasLineSequence ? 'Yes' : 'No'}</p>
      </div>
    </div>
  );
}
