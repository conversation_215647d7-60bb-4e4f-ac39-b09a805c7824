/**
 * Next.js 使用示例
 * 展示如何在 Next.js 项目中使用 String Art Generator
 */

import { useState, useRef, useCallback } from 'react';
import StringArtGenerator from '../src/modules/stringArtGenerator.js';

// React Hook 示例
export function useStringArtGenerator(config = {}) {
  const [generator] = useState(() => new StringArtGenerator(config));
  const [state, setState] = useState(generator.getState());
  const [result, setResult] = useState(null);
  const [error, setError] = useState(null);

  const updateState = useCallback(() => {
    setState(generator.getState());
  }, [generator]);

  const processImage = useCallback(async (imageInput, canvas) => {
    try {
      setError(null);
      updateState();

      const result = await generator.processImage(imageInput, canvas);
      updateState();
      return result;
    } catch (err) {
      setError(err.message);
      updateState();
      throw err;
    }
  }, [generator, updateState]);

  const generateStringArt = useCallback(async (options = {}) => {
    try {
      setError(null);
      updateState();

      const result = await generator.generateStringArt({
        ...options,
        onProgress: (progress) => {
          updateState();
          options.onProgress?.(progress);
        }
      });

      setResult(result);
      updateState();
      return result;
    } catch (err) {
      setError(err.message);
      updateState();
      throw err;
    }
  }, [generator, updateState]);

  const reset = useCallback(() => {
    generator.reset();
    setResult(null);
    setError(null);
    updateState();
  }, [generator, updateState]);

  return {
    generator,
    state,
    result,
    error,
    processImage,
    generateStringArt,
    reset
  };
}

// React 组件示例
// 简单的API使用示例
export async function simpleStringArtExample() {
  // 创建生成器实例
  const generator = new StringArtGenerator({
    imageSize: 500,
    numberOfPins: 288,
    maxLines: 2000,
    lineWeight: 20
  });

  // 创建画布
  const canvas = document.createElement('canvas');
  const previewCanvas = document.createElement('canvas');

  try {
    // 1. 处理图像（从URL加载）
    console.log('Processing image...');
    await generator.processImage('path/to/your/image.jpg', previewCanvas);

    // 2. 生成String Art
    console.log('Generating string art...');
    const result = await generator.generateStringArt({
      canvas: canvas,
      onProgress: (progress) => {
        console.log(`Progress: ${Math.round(progress.progress * 100)}%`);
      }
    });

    // 3. 导出结果
    console.log('Generation complete!');
    console.log(`Total lines: ${result.totalLines}`);
    console.log(`Thread length: ${result.threadLength.toFixed(2)}`);

    // 导出图像
    generator.renderer.downloadImage(canvas, 'my-string-art');

    // 导出线条序列
    const sequence = generator.exportLineSequence();
    console.log('Line sequence:', sequence);

    return result;
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
}

// React 组件示例
export default function StringArtGeneratorComponent() {
  const canvasRef = useRef(null);
  const previewCanvasRef = useRef(null);
  const fileInputRef = useRef(null);

  const [config, setConfig] = useState({
    imageSize: 500,
    numberOfPins: 288,
    maxLines: 4000,
    lineWeight: 20,
    minDistance: 20
  });

  const {
    generator,
    state,
    result,
    error,
    processImage,
    generateStringArt,
    reset
  } = useStringArtGenerator(config);

  const [progress, setProgress] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      await processImage(file, previewCanvasRef.current);
    } catch (err) {
      console.error('Failed to process image:', err);
    }
  };

  const handleGenerate = async () => {
    if (!state.hasProcessedImage) {
      alert('Please select an image first');
      return;
    }

    setIsGenerating(true);
    setProgress(0);

    try {
      await generateStringArt({
        canvas: canvasRef.current,
        onProgress: (progressData) => {
          setProgress(progressData.progress);
        }
      });
    } catch (err) {
      console.error('Failed to generate string art:', err);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleConfigChange = (key, value) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDownload = () => {
    if (canvasRef.current) {
      generator.renderer.downloadImage(canvasRef.current, 'string-art');
    }
  };

  const handleExportSequence = () => {
    if (result) {
      const sequence = generator.exportLineSequence();
      const blob = new Blob([sequence], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'string-art-sequence.txt';
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="string-art-generator">
      <h1>String Art Generator</h1>

      {/* 配置面板 */}
      <div className="config-panel">
        <h3>Configuration</h3>
        <div className="config-grid">
          <label>
            Number of Pins:
            <input
              type="number"
              value={config.numberOfPins}
              onChange={(e) => handleConfigChange('numberOfPins', parseInt(e.target.value))}
              min="50"
              max="500"
            />
          </label>

          <label>
            Max Lines:
            <input
              type="number"
              value={config.maxLines}
              onChange={(e) => handleConfigChange('maxLines', parseInt(e.target.value))}
              min="100"
              max="10000"
            />
          </label>

          <label>
            Line Weight:
            <input
              type="number"
              value={config.lineWeight}
              onChange={(e) => handleConfigChange('lineWeight', parseInt(e.target.value))}
              min="1"
              max="50"
            />
          </label>

          <label>
            Min Distance:
            <input
              type="number"
              value={config.minDistance}
              onChange={(e) => handleConfigChange('minDistance', parseInt(e.target.value))}
              min="5"
              max="50"
            />
          </label>
        </div>
      </div>

      {/* 文件选择 */}
      <div className="file-input-section">
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          style={{ display: 'none' }}
        />
        <button onClick={() => fileInputRef.current?.click()}>
          Select Image
        </button>
      </div>

      {/* 预览画布 */}
      {state.hasProcessedImage && (
        <div className="preview-section">
          <h3>Processed Image</h3>
          <canvas
            ref={previewCanvasRef}
            style={{ border: '1px solid #ccc', maxWidth: '400px' }}
          />
        </div>
      )}

      {/* 生成按钮和进度 */}
      <div className="generation-section">
        <button
          onClick={handleGenerate}
          disabled={!state.hasProcessedImage || isGenerating}
        >
          {isGenerating ? 'Generating...' : 'Generate String Art'}
        </button>

        {isGenerating && (
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${progress * 100}%` }}
            />
            <span>{Math.round(progress * 100)}%</span>
          </div>
        )}
      </div>

      {/* 结果画布 */}
      {result && (
        <div className="result-section">
          <h3>String Art Result</h3>
          <canvas
            ref={canvasRef}
            style={{ border: '1px solid #ccc', maxWidth: '800px' }}
          />

          <div className="result-info">
            <p>Total Lines: {result.totalLines}</p>
            <p>Thread Length: {result.threadLength.toFixed(2)} units</p>
          </div>

          <div className="result-actions">
            <button onClick={handleDownload}>Download Image</button>
            <button onClick={handleExportSequence}>Export Sequence</button>
          </div>
        </div>
      )}

      {/* 错误显示 */}
      {error && (
        <div className="error-message">
          Error: {error}
        </div>
      )}

      {/* 重置按钮 */}
      <div className="reset-section">
        <button onClick={reset}>Reset</button>
      </div>

      <style jsx>{`
        .string-art-generator {
          max-width: 1200px;
          margin: 0 auto;
          padding: 20px;
        }
        
        .config-panel {
          background: #f5f5f5;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        
        .config-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;
        }
        
        .config-grid label {
          display: flex;
          flex-direction: column;
          gap: 5px;
        }
        
        .config-grid input {
          padding: 8px;
          border: 1px solid #ddd;
          border-radius: 4px;
        }
        
        .file-input-section {
          margin-bottom: 20px;
        }
        
        .preview-section,
        .result-section {
          margin-bottom: 20px;
        }
        
        .generation-section {
          margin-bottom: 20px;
        }
        
        .progress-bar {
          position: relative;
          width: 100%;
          height: 20px;
          background: #e0e0e0;
          border-radius: 10px;
          margin-top: 10px;
          overflow: hidden;
        }
        
        .progress-fill {
          height: 100%;
          background: #4caf50;
          transition: width 0.3s ease;
        }
        
        .progress-bar span {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 12px;
          font-weight: bold;
        }
        
        .result-info {
          margin: 10px 0;
        }
        
        .result-actions {
          display: flex;
          gap: 10px;
        }
        
        .error-message {
          background: #ffebee;
          color: #c62828;
          padding: 10px;
          border-radius: 4px;
          margin-bottom: 20px;
        }
        
        button {
          background: #2196f3;
          color: white;
          border: none;
          padding: 10px 20px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
        }
        
        button:hover:not(:disabled) {
          background: #1976d2;
        }
        
        button:disabled {
          background: #ccc;
          cursor: not-allowed;
        }
        
        canvas {
          display: block;
          margin: 10px 0;
        }
      `}</style>
    </div>
  );
}
