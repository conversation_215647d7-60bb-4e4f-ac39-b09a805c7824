<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>String Art Generator - Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover:not(:disabled) {
            background: #1976d2;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        input[type="file"] {
            margin-bottom: 10px;
        }
        
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
            display: block;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #4caf50;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .info {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .config-grid label {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .config-grid input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>String Art Generator - 模块化测试</h1>
    
    <!-- 配置区域 -->
    <div class="section">
        <h2>配置</h2>
        <div class="config-grid">
            <label>
                图像尺寸:
                <input type="number" id="imageSize" value="500" min="100" max="1000">
            </label>
            <label>
                钉子数量:
                <input type="number" id="numberOfPins" value="288" min="50" max="500">
            </label>
            <label>
                最大线条数:
                <input type="number" id="maxLines" value="2000" min="100" max="10000">
            </label>
            <label>
                线条权重:
                <input type="number" id="lineWeight" value="20" min="1" max="50">
            </label>
        </div>
    </div>
    
    <!-- 图像输入区域 -->
    <div class="section">
        <h2>图像输入</h2>
        <input type="file" id="fileInput" accept="image/*">
        <div class="controls">
            <button onclick="loadTestImage()">加载测试图像</button>
            <button onclick="processImage()" id="processBtn" disabled>处理图像</button>
        </div>
        <canvas id="previewCanvas" width="500" height="500"></canvas>
    </div>
    
    <!-- 生成区域 -->
    <div class="section">
        <h2>String Art 生成</h2>
        <div class="controls">
            <button onclick="generateStringArt()" id="generateBtn" disabled>生成 String Art</button>
            <button onclick="stopGeneration()" id="stopBtn" disabled>停止生成</button>
            <button onclick="resetGenerator()">重置</button>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="statusInfo" class="info" style="display: none;"></div>
        <div id="errorInfo" class="error" style="display: none;"></div>
        
        <canvas id="resultCanvas" width="1000" height="1000"></canvas>
    </div>
    
    <!-- 结果区域 -->
    <div class="section">
        <h2>结果操作</h2>
        <div class="controls">
            <button onclick="downloadImage()" id="downloadBtn" disabled>下载图像</button>
            <button onclick="exportSequence()" id="exportBtn" disabled>导出序列</button>
            <button onclick="showInteractiveControls()" id="interactiveBtn" disabled>交互式播放</button>
        </div>
        
        <div id="interactiveControls" style="display: none;">
            <h3>交互式控制</h3>
            <div class="controls">
                <button onclick="interactive.reset()">重置</button>
                <button onclick="interactive.previousStep()">上一步</button>
                <button onclick="interactive.nextStep()">下一步</button>
                <button onclick="interactive.complete()">完成</button>
            </div>
            <div>
                步骤: <span id="currentStep">0</span> / <span id="totalSteps">0</span>
            </div>
        </div>
        
        <div id="resultInfo" class="info" style="display: none;"></div>
    </div>

    <!-- 引入模块 -->
    <script type="module">
        // 由于这是一个简单的测试页面，我们需要模拟模块导入
        // 在实际的Next.js项目中，你可以直接使用 import 语句
        
        // 这里我们创建一个简化版本来演示API的使用
        class SimpleStringArtTest {
            constructor() {
                this.generator = null;
                this.isGenerating = false;
                this.interactive = null;
                this.result = null;
            }
            
            init() {
                this.updateConfig();
                this.setupEventListeners();
                this.showInfo('模块化 String Art Generator 已初始化');
            }
            
            updateConfig() {
                const config = {
                    imageSize: parseInt(document.getElementById('imageSize').value),
                    numberOfPins: parseInt(document.getElementById('numberOfPins').value),
                    maxLines: parseInt(document.getElementById('maxLines').value),
                    lineWeight: parseInt(document.getElementById('lineWeight').value),
                    minDistance: 20,
                    scale: 2
                };
                
                // 在实际项目中，这里会创建 StringArtGenerator 实例
                // this.generator = new StringArtGenerator(config);
                console.log('配置更新:', config);
            }
            
            setupEventListeners() {
                document.getElementById('fileInput').addEventListener('change', (e) => {
                    if (e.target.files[0]) {
                        document.getElementById('processBtn').disabled = false;
                    }
                });
                
                // 配置变化时更新
                ['imageSize', 'numberOfPins', 'maxLines', 'lineWeight'].forEach(id => {
                    document.getElementById(id).addEventListener('change', () => {
                        this.updateConfig();
                    });
                });
            }
            
            async processImage() {
                const fileInput = document.getElementById('fileInput');
                const canvas = document.getElementById('previewCanvas');
                
                if (!fileInput.files[0]) {
                    this.showError('请选择一个图像文件');
                    return;
                }
                
                try {
                    this.showInfo('正在处理图像...');
                    
                    // 模拟图像处理
                    await this.simulateImageProcessing(fileInput.files[0], canvas);
                    
                    this.showInfo('图像处理完成');
                    document.getElementById('generateBtn').disabled = false;
                } catch (error) {
                    this.showError('图像处理失败: ' + error.message);
                }
            }
            
            async simulateImageProcessing(file, canvas) {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = () => {
                        const ctx = canvas.getContext('2d');
                        const size = parseInt(document.getElementById('imageSize').value);
                        
                        canvas.width = size;
                        canvas.height = size;
                        
                        // 简单的图像绘制
                        ctx.drawImage(img, 0, 0, size, size);
                        
                        // 应用圆形遮罩
                        ctx.globalCompositeOperation = 'destination-in';
                        ctx.beginPath();
                        ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
                        ctx.fill();
                        ctx.globalCompositeOperation = 'source-over';
                        
                        resolve();
                    };
                    img.src = URL.createObjectURL(file);
                });
            }
            
            async generateStringArt() {
                if (this.isGenerating) return;
                
                this.isGenerating = true;
                document.getElementById('generateBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                
                try {
                    this.showInfo('正在生成 String Art...');
                    
                    // 模拟生成过程
                    await this.simulateGeneration();
                    
                    this.showInfo('String Art 生成完成!');
                    this.enableResultButtons();
                } catch (error) {
                    this.showError('生成失败: ' + error.message);
                } finally {
                    this.isGenerating = false;
                    document.getElementById('generateBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                }
            }
            
            async simulateGeneration() {
                const canvas = document.getElementById('resultCanvas');
                const ctx = canvas.getContext('2d');
                const maxLines = parseInt(document.getElementById('maxLines').value);
                const numberOfPins = parseInt(document.getElementById('numberOfPins').value);
                
                canvas.width = 1000;
                canvas.height = 1000;
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, 1000, 1000);
                
                // 模拟钉子位置
                const pins = [];
                const center = 500;
                const radius = 480;
                
                for (let i = 0; i < numberOfPins; i++) {
                    const angle = 2 * Math.PI * i / numberOfPins;
                    pins.push([
                        center + radius * Math.cos(angle),
                        center + radius * Math.sin(angle)
                    ]);
                }
                
                // 模拟线条生成
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 0.5;
                
                let currentPin = 0;
                const sequence = [currentPin];
                
                for (let i = 0; i < maxLines && this.isGenerating; i++) {
                    // 随机选择下一个钉子（简化版本）
                    const nextPin = (currentPin + Math.floor(Math.random() * (numberOfPins - 40)) + 20) % numberOfPins;
                    
                    // 绘制线条
                    ctx.beginPath();
                    ctx.moveTo(pins[currentPin][0], pins[currentPin][1]);
                    ctx.lineTo(pins[nextPin][0], pins[nextPin][1]);
                    ctx.stroke();
                    
                    sequence.push(nextPin);
                    currentPin = nextPin;
                    
                    // 更新进度
                    const progress = (i + 1) / maxLines;
                    this.updateProgress(progress);
                    this.showInfo(`生成中... ${i + 1}/${maxLines} 线条`);
                    
                    // 每10条线暂停一下，允许UI更新
                    if (i % 10 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }
                
                this.result = {
                    sequence,
                    totalLines: sequence.length - 1,
                    pins
                };
            }
            
            updateProgress(progress) {
                const progressBar = document.getElementById('progressBar');
                progressBar.style.width = (progress * 100) + '%';
            }
            
            enableResultButtons() {
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('exportBtn').disabled = false;
                document.getElementById('interactiveBtn').disabled = false;
                
                if (this.result) {
                    document.getElementById('resultInfo').style.display = 'block';
                    document.getElementById('resultInfo').innerHTML = `
                        <strong>生成结果:</strong><br>
                        总线条数: ${this.result.totalLines}<br>
                        钉子数量: ${this.result.pins.length}
                    `;
                }
            }
            
            downloadImage() {
                const canvas = document.getElementById('resultCanvas');
                const link = document.createElement('a');
                link.download = 'string-art.png';
                link.href = canvas.toDataURL();
                link.click();
            }
            
            exportSequence() {
                if (!this.result) return;
                
                const sequence = this.result.sequence.join(',');
                const blob = new Blob([sequence], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.download = 'string-art-sequence.txt';
                link.href = url;
                link.click();
                URL.revokeObjectURL(url);
            }
            
            showInteractiveControls() {
                if (!this.result) return;
                
                document.getElementById('interactiveControls').style.display = 'block';
                document.getElementById('totalSteps').textContent = this.result.totalLines;
                
                this.interactive = this.createInteractiveRenderer();
            }
            
            createInteractiveRenderer() {
                const canvas = document.getElementById('resultCanvas');
                let currentStep = 0;
                
                return {
                    currentStep,
                    reset: () => {
                        currentStep = 0;
                        this.renderStep(canvas, currentStep);
                        document.getElementById('currentStep').textContent = currentStep;
                    },
                    nextStep: () => {
                        if (currentStep < this.result.totalLines) {
                            currentStep++;
                            this.renderStep(canvas, currentStep);
                            document.getElementById('currentStep').textContent = currentStep;
                        }
                    },
                    previousStep: () => {
                        if (currentStep > 0) {
                            currentStep--;
                            this.renderStep(canvas, currentStep);
                            document.getElementById('currentStep').textContent = currentStep;
                        }
                    },
                    complete: () => {
                        currentStep = this.result.totalLines;
                        this.renderStep(canvas, currentStep);
                        document.getElementById('currentStep').textContent = currentStep;
                    }
                };
            }
            
            renderStep(canvas, step) {
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, 1000, 1000);
                
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 0.5;
                
                for (let i = 1; i <= step && i < this.result.sequence.length; i++) {
                    const fromPin = this.result.sequence[i - 1];
                    const toPin = this.result.sequence[i];
                    
                    ctx.beginPath();
                    ctx.moveTo(this.result.pins[fromPin][0], this.result.pins[fromPin][1]);
                    ctx.lineTo(this.result.pins[toPin][0], this.result.pins[toPin][1]);
                    ctx.stroke();
                }
                
                // 高亮当前线条
                if (step > 0 && step < this.result.sequence.length) {
                    const fromPin = this.result.sequence[step - 1];
                    const toPin = this.result.sequence[step];
                    
                    ctx.strokeStyle = 'red';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.moveTo(this.result.pins[fromPin][0], this.result.pins[fromPin][1]);
                    ctx.lineTo(this.result.pins[toPin][0], this.result.pins[toPin][1]);
                    ctx.stroke();
                }
            }
            
            stopGeneration() {
                this.isGenerating = false;
                this.showInfo('生成已停止');
            }
            
            resetGenerator() {
                this.isGenerating = false;
                this.result = null;
                this.interactive = null;
                
                document.getElementById('generateBtn').disabled = true;
                document.getElementById('downloadBtn').disabled = true;
                document.getElementById('exportBtn').disabled = true;
                document.getElementById('interactiveBtn').disabled = true;
                document.getElementById('interactiveControls').style.display = 'none';
                document.getElementById('resultInfo').style.display = 'none';
                
                this.updateProgress(0);
                this.showInfo('生成器已重置');
                
                // 清空画布
                const canvases = ['previewCanvas', 'resultCanvas'];
                canvases.forEach(id => {
                    const canvas = document.getElementById(id);
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                });
            }
            
            loadTestImage() {
                // 创建一个简单的测试图像
                const canvas = document.getElementById('previewCanvas');
                const ctx = canvas.getContext('2d');
                const size = parseInt(document.getElementById('imageSize').value);
                
                canvas.width = size;
                canvas.height = size;
                
                // 绘制一个简单的测试图案
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, size, size);
                
                ctx.fillStyle = 'black';
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/4, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(size/2 - size/8, size/2 - size/8, size/16, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.beginPath();
                ctx.arc(size/2 + size/8, size/2 - size/8, size/16, 0, Math.PI * 2);
                ctx.fill();
                
                // 应用圆形遮罩
                ctx.globalCompositeOperation = 'destination-in';
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalCompositeOperation = 'source-over';
                
                document.getElementById('generateBtn').disabled = false;
                this.showInfo('测试图像已加载');
            }
            
            showInfo(message) {
                const info = document.getElementById('statusInfo');
                const error = document.getElementById('errorInfo');
                
                info.textContent = message;
                info.style.display = 'block';
                error.style.display = 'none';
            }
            
            showError(message) {
                const info = document.getElementById('statusInfo');
                const error = document.getElementById('errorInfo');
                
                error.textContent = message;
                error.style.display = 'block';
                info.style.display = 'none';
            }
        }
        
        // 全局变量和函数，供HTML中的onclick使用
        window.stringArtTest = new SimpleStringArtTest();
        window.interactive = null;
        
        window.processImage = () => stringArtTest.processImage();
        window.generateStringArt = () => stringArtTest.generateStringArt();
        window.stopGeneration = () => stringArtTest.stopGeneration();
        window.resetGenerator = () => stringArtTest.resetGenerator();
        window.loadTestImage = () => stringArtTest.loadTestImage();
        window.downloadImage = () => stringArtTest.downloadImage();
        window.exportSequence = () => stringArtTest.exportSequence();
        window.showInteractiveControls = () => {
            stringArtTest.showInteractiveControls();
            window.interactive = stringArtTest.interactive;
        };
        
        // 初始化
        stringArtTest.init();
    </script>
</body>
</html>
