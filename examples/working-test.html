<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Working String Art Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 10px 20px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover:not(:disabled) {
            background: #1976d2;
        }
        
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
            display: block;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #4caf50;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .info {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .config-grid label {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .config-grid input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Working String Art Generator</h1>
    
    <!-- 配置区域 -->
    <div class="section">
        <h2>配置</h2>
        <div class="config-grid">
            <label>
                图像尺寸:
                <input type="number" id="imageSize" value="300" min="100" max="500">
            </label>
            <label>
                钉子数量:
                <input type="number" id="numberOfPins" value="200" min="50" max="400">
            </label>
            <label>
                最大线条数:
                <input type="number" id="maxLines" value="1500" min="100" max="5000">
            </label>
            <label>
                线条权重:
                <input type="number" id="lineWeight" value="15" min="1" max="50">
            </label>
        </div>
    </div>
    
    <!-- 图像输入区域 -->
    <div class="section">
        <h2>图像输入</h2>
        <input type="file" id="fileInput" accept="image/*">
        <div class="controls">
            <button onclick="loadTestImage()">加载测试图像</button>
            <button onclick="processImage()" id="processBtn" disabled>处理图像</button>
        </div>
        <canvas id="previewCanvas" width="300" height="300"></canvas>
    </div>
    
    <!-- 生成区域 -->
    <div class="section">
        <h2>String Art 生成</h2>
        <div class="controls">
            <button onclick="generateStringArt()" id="generateBtn" disabled>生成 String Art</button>
            <button onclick="stopGeneration()" id="stopBtn" disabled>停止生成</button>
            <button onclick="resetGenerator()">重置</button>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="statusInfo" class="info" style="display: none;"></div>
        <div id="errorInfo" class="error" style="display: none;"></div>
        
        <canvas id="resultCanvas" width="600" height="600"></canvas>
    </div>
    
    <!-- 结果区域 -->
    <div class="section">
        <h2>结果操作</h2>
        <div class="controls">
            <button onclick="downloadImage()" id="downloadBtn" disabled>下载图像</button>
            <button onclick="exportSequence()" id="exportBtn" disabled>导出序列</button>
        </div>
        
        <div id="resultInfo" class="info" style="display: none;"></div>
    </div>

    <script type="module">
        // 真正的String Art Generator实现
        class RealStringArtGenerator {
            constructor() {
                this.isGenerating = false;
                this.result = null;
                this.processedImageData = null;
                this.config = this.getConfig();
            }
            
            getConfig() {
                return {
                    imageSize: parseInt(document.getElementById('imageSize').value),
                    numberOfPins: parseInt(document.getElementById('numberOfPins').value),
                    maxLines: parseInt(document.getElementById('maxLines').value),
                    lineWeight: parseInt(document.getElementById('lineWeight').value),
                    minDistance: 15
                };
            }
            
            init() {
                this.setupEventListeners();
                this.showInfo('String Art Generator 已初始化');
            }
            
            setupEventListeners() {
                document.getElementById('fileInput').addEventListener('change', (e) => {
                    if (e.target.files[0]) {
                        document.getElementById('processBtn').disabled = false;
                    }
                });
                
                // 配置变化时更新
                ['imageSize', 'numberOfPins', 'maxLines', 'lineWeight'].forEach(id => {
                    document.getElementById(id).addEventListener('change', () => {
                        this.config = this.getConfig();
                        // 如果配置改变，需要重新处理图像
                        if (this.processedImageData) {
                            document.getElementById('generateBtn').disabled = true;
                            this.showInfo('配置已更改，请重新处理图像');
                        }
                    });
                });
            }
            
            async processImage() {
                const fileInput = document.getElementById('fileInput');
                const canvas = document.getElementById('previewCanvas');
                
                if (!fileInput.files[0]) {
                    this.showError('请选择一个图像文件');
                    return;
                }
                
                try {
                    this.showInfo('正在处理图像...');
                    this.config = this.getConfig();
                    
                    // 真正的图像处理
                    this.processedImageData = await this.realImageProcessing(fileInput.files[0], canvas);
                    
                    this.showInfo('图像处理完成');
                    document.getElementById('generateBtn').disabled = false;
                } catch (error) {
                    this.showError('图像处理失败: ' + error.message);
                }
            }
            
            async realImageProcessing(file, canvas) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = () => {
                        try {
                            const ctx = canvas.getContext('2d');
                            const size = this.config.imageSize;
                            
                            canvas.width = size;
                            canvas.height = size;
                            
                            // 1. 裁剪为正方形并调整大小
                            let selectedWidth = img.width;
                            let selectedHeight = img.height;
                            let xOffset = 0;
                            let yOffset = 0;
                            
                            if (img.height > img.width) {
                                selectedWidth = img.width;
                                selectedHeight = img.width;
                                yOffset = Math.floor((img.height - img.width) / 2);
                            } else if (img.width > img.height) {
                                selectedWidth = img.height;
                                selectedHeight = img.height;
                                xOffset = Math.floor((img.width - img.height) / 2);
                            }
                            
                            ctx.drawImage(img, xOffset, yOffset, selectedWidth, selectedHeight, 0, 0, size, size);
                            
                            // 2. 转换为灰度
                            const imageData = ctx.getImageData(0, 0, size, size);
                            const grayscaleData = [];
                            
                            for (let i = 0; i < imageData.data.length; i += 4) {
                                const avg = (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) / 3;
                                imageData.data[i] = avg;     // R
                                imageData.data[i + 1] = avg; // G
                                imageData.data[i + 2] = avg; // B
                                grayscaleData.push(avg);
                            }
                            
                            ctx.putImageData(imageData, 0, 0);
                            
                            // 3. 应用圆形遮罩
                            ctx.globalCompositeOperation = 'destination-in';
                            ctx.beginPath();
                            ctx.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
                            ctx.closePath();
                            ctx.fill();
                            ctx.globalCompositeOperation = 'source-over';
                            
                            resolve(grayscaleData);
                        } catch (error) {
                            reject(error);
                        }
                    };
                    img.onerror = reject;
                    img.src = URL.createObjectURL(file);
                });
            }
            
            async generateStringArt() {
                if (this.isGenerating || !this.processedImageData) return;
                
                this.isGenerating = true;
                document.getElementById('generateBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                
                try {
                    this.showInfo('正在生成 String Art...');
                    this.config = this.getConfig();
                    
                    // 真正的String Art生成算法
                    const result = await this.realStringArtGeneration();
                    
                    this.result = result;
                    this.showInfo(`String Art 生成完成! 总共 ${result.totalLines} 条线`);
                    this.enableResultButtons();
                } catch (error) {
                    this.showError('生成失败: ' + error.message);
                } finally {
                    this.isGenerating = false;
                    document.getElementById('generateBtn').disabled = false;
                    document.getElementById('stopBtn').disabled = true;
                }
            }
            
            async realStringArtGeneration() {
                const canvas = document.getElementById('resultCanvas');
                const ctx = canvas.getContext('2d');
                const size = this.config.imageSize;
                const scale = 600 / size; // 缩放到600x600显示
                
                canvas.width = 600;
                canvas.height = 600;
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, 600, 600);
                
                // 1. 计算钉子位置
                this.showInfo('计算钉子位置...');
                const pins = this.calculatePinPositions();
                this.updateProgress(0.1);
                
                // 2. 预计算线条
                this.showInfo('预计算线条...');
                const lineCache = await this.precalculateLines(pins);
                this.updateProgress(0.2);
                
                // 3. 初始化误差数组（这是关键！）
                const errorArray = this.processedImageData.map(pixel => 255 - pixel);
                
                // 4. 生成线条序列
                this.showInfo('生成线条序列...');
                const sequence = await this.generateLineSequence(errorArray, pins, lineCache, ctx, scale);
                
                return {
                    sequence,
                    totalLines: sequence.length - 1,
                    pins: pins.map(pin => [pin[0] * scale, pin[1] * scale])
                };
            }
            
            calculatePinPositions() {
                const pins = [];
                const center = this.config.imageSize / 2;
                const radius = this.config.imageSize / 2 - 1;
                
                for (let i = 0; i < this.config.numberOfPins; i++) {
                    const angle = 2 * Math.PI * i / this.config.numberOfPins;
                    const x = center + radius * Math.cos(angle);
                    const y = center + radius * Math.sin(angle);
                    pins.push([Math.floor(x), Math.floor(y)]);
                }
                
                return pins;
            }
            
            async precalculateLines(pins) {
                const cache = new Map();
                let processed = 0;
                const total = this.config.numberOfPins * (this.config.numberOfPins - this.config.minDistance) / 2;
                
                for (let a = 0; a < this.config.numberOfPins; a++) {
                    for (let b = a + this.config.minDistance; b < this.config.numberOfPins; b++) {
                        const coords = this.calculateLineCoords(pins[a], pins[b]);
                        cache.set(`${a}-${b}`, coords);
                        cache.set(`${b}-${a}`, coords);
                        
                        processed++;
                        if (processed % 100 === 0) {
                            this.updateProgress(0.1 + (processed / total) * 0.1);
                            await new Promise(resolve => setTimeout(resolve, 1));
                        }
                    }
                }
                
                return cache;
            }
            
            calculateLineCoords(pin1, pin2) {
                const [x0, y0] = pin1;
                const [x1, y1] = pin2;
                const distance = Math.floor(Math.sqrt((x1-x0)*(x1-x0) + (y1-y0)*(y1-y0)));
                
                const coords = [];
                for (let i = 0; i < distance; i++) {
                    const t = i / distance;
                    const x = Math.floor(x0 + t * (x1 - x0));
                    const y = Math.floor(y0 + t * (y1 - y0));
                    if (x >= 0 && x < this.config.imageSize && y >= 0 && y < this.config.imageSize) {
                        coords.push([x, y]);
                    }
                }
                
                return coords;
            }
            
            async generateLineSequence(errorArray, pins, lineCache, ctx, scale) {
                const sequence = [0];
                let currentPin = 0;
                const recentPins = [];
                
                ctx.strokeStyle = 'black';
                ctx.lineWidth = 0.8;
                
                for (let lineIndex = 0; lineIndex < this.config.maxLines && this.isGenerating; lineIndex++) {
                    // 寻找最佳下一个钉子
                    let bestPin = -1;
                    let maxError = -1;
                    
                    for (let offset = this.config.minDistance; offset < this.config.numberOfPins - this.config.minDistance; offset++) {
                        const testPin = (currentPin + offset) % this.config.numberOfPins;
                        
                        if (recentPins.includes(testPin)) continue;
                        
                        const key = `${currentPin}-${testPin}`;
                        const coords = lineCache.get(key);
                        
                        if (coords) {
                            const lineError = this.calculateLineError(errorArray, coords);
                            if (lineError > maxError) {
                                maxError = lineError;
                                bestPin = testPin;
                            }
                        }
                    }
                    
                    if (bestPin === -1) break;
                    
                    // 绘制线条
                    const [x0, y0] = pins[currentPin];
                    const [x1, y1] = pins[bestPin];
                    
                    ctx.beginPath();
                    ctx.moveTo(x0 * scale, y0 * scale);
                    ctx.lineTo(x1 * scale, y1 * scale);
                    ctx.stroke();
                    
                    // 更新误差数组（这是关键步骤！）
                    const coords = lineCache.get(`${currentPin}-${bestPin}`);
                    this.updateErrorArray(errorArray, coords);
                    
                    // 更新状态
                    sequence.push(bestPin);
                    recentPins.push(bestPin);
                    if (recentPins.length > 20) recentPins.shift();
                    currentPin = bestPin;
                    
                    // 更新进度
                    const progress = 0.2 + (lineIndex / this.config.maxLines) * 0.8;
                    this.updateProgress(progress);
                    this.showInfo(`生成中... ${lineIndex + 1}/${this.config.maxLines} 条线`);
                    
                    // 每10条线暂停一下
                    if (lineIndex % 10 === 0) {
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }
                
                return sequence;
            }
            
            calculateLineError(errorArray, coords) {
                let totalError = 0;
                for (const [x, y] of coords) {
                    const index = y * this.config.imageSize + x;
                    if (index >= 0 && index < errorArray.length) {
                        totalError += errorArray[index];
                    }
                }
                return totalError;
            }
            
            updateErrorArray(errorArray, coords) {
                for (const [x, y] of coords) {
                    const index = y * this.config.imageSize + x;
                    if (index >= 0 && index < errorArray.length) {
                        errorArray[index] = Math.max(0, errorArray[index] - this.config.lineWeight);
                    }
                }
            }
            
            updateProgress(progress) {
                document.getElementById('progressBar').style.width = (progress * 100) + '%';
            }
            
            enableResultButtons() {
                document.getElementById('downloadBtn').disabled = false;
                document.getElementById('exportBtn').disabled = false;
                
                if (this.result) {
                    document.getElementById('resultInfo').style.display = 'block';
                    document.getElementById('resultInfo').innerHTML = `
                        <strong>生成结果:</strong><br>
                        总线条数: ${this.result.totalLines}<br>
                        钉子数量: ${this.config.numberOfPins}
                    `;
                }
            }
            
            downloadImage() {
                const canvas = document.getElementById('resultCanvas');
                const link = document.createElement('a');
                link.download = 'string-art.png';
                link.href = canvas.toDataURL();
                link.click();
            }
            
            exportSequence() {
                if (!this.result) return;
                
                const sequence = this.result.sequence.join(',');
                const blob = new Blob([sequence], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.download = 'string-art-sequence.txt';
                link.href = url;
                link.click();
                URL.revokeObjectURL(url);
            }
            
            stopGeneration() {
                this.isGenerating = false;
                this.showInfo('生成已停止');
            }
            
            resetGenerator() {
                this.isGenerating = false;
                this.result = null;
                this.processedImageData = null;
                
                document.getElementById('generateBtn').disabled = true;
                document.getElementById('downloadBtn').disabled = true;
                document.getElementById('exportBtn').disabled = true;
                document.getElementById('resultInfo').style.display = 'none';
                
                this.updateProgress(0);
                this.showInfo('生成器已重置');
                
                // 清空画布
                const canvases = ['previewCanvas', 'resultCanvas'];
                canvases.forEach(id => {
                    const canvas = document.getElementById(id);
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                });
            }
            
            loadTestImage() {
                // 创建一个更复杂的测试图像
                const canvas = document.getElementById('previewCanvas');
                const ctx = canvas.getContext('2d');
                const size = this.config.imageSize;
                
                canvas.width = size;
                canvas.height = size;
                
                // 绘制测试图案
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, size, size);
                
                // 绘制人脸轮廓
                ctx.fillStyle = 'black';
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/3, 0, Math.PI * 2);
                ctx.fill();
                
                // 眼睛
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(size/2 - size/8, size/2 - size/12, size/20, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.beginPath();
                ctx.arc(size/2 + size/8, size/2 - size/12, size/20, 0, Math.PI * 2);
                ctx.fill();
                
                // 嘴巴
                ctx.beginPath();
                ctx.arc(size/2, size/2 + size/8, size/16, 0, Math.PI, false);
                ctx.fill();
                
                // 应用圆形遮罩
                ctx.globalCompositeOperation = 'destination-in';
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2, 0, Math.PI * 2);
                ctx.fill();
                ctx.globalCompositeOperation = 'source-over';
                
                // 获取图像数据
                const imageData = ctx.getImageData(0, 0, size, size);
                const grayscaleData = [];
                
                for (let i = 0; i < imageData.data.length; i += 4) {
                    const avg = (imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2]) / 3;
                    grayscaleData.push(avg);
                }
                
                this.processedImageData = grayscaleData;
                document.getElementById('generateBtn').disabled = false;
                this.showInfo('测试图像已加载');
            }
            
            showInfo(message) {
                const info = document.getElementById('statusInfo');
                const error = document.getElementById('errorInfo');
                
                info.textContent = message;
                info.style.display = 'block';
                error.style.display = 'none';
            }
            
            showError(message) {
                const info = document.getElementById('statusInfo');
                const error = document.getElementById('errorInfo');
                
                error.textContent = message;
                error.style.display = 'block';
                info.style.display = 'none';
            }
        }
        
        // 全局变量和函数
        window.stringArtGenerator = new RealStringArtGenerator();
        
        window.processImage = () => stringArtGenerator.processImage();
        window.generateStringArt = () => stringArtGenerator.generateStringArt();
        window.stopGeneration = () => stringArtGenerator.stopGeneration();
        window.resetGenerator = () => stringArtGenerator.resetGenerator();
        window.loadTestImage = () => stringArtGenerator.loadTestImage();
        window.downloadImage = () => stringArtGenerator.downloadImage();
        window.exportSequence = () => stringArtGenerator.exportSequence();
        
        // 初始化
        stringArtGenerator.init();
    </script>
</body>
</html>
