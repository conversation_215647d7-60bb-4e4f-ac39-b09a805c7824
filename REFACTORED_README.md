# String Art Generator - 重构版本

这是一个重构后的String Art Generator，将原本与页面深度绑定的代码分解为可复用的模块，方便在Next.js或其他项目中使用。

## 模块结构

### 核心模块

1. **ImageProcessor** (`src/modules/imageProcessor.js`)
   - 图像加载、裁剪、灰度化、圆形遮罩
   - 支持文件、URL、HTMLImageElement输入

2. **GeometryCalculator** (`src/modules/geometryCalculator.js`)
   - 钉子位置计算
   - 线条路径计算
   - 几何相关工具函数

3. **StringArtAlgorithm** (`src/modules/stringArtAlgorithm.js`)
   - String Art生成核心算法
   - 误差计算和线条优化
   - 线条序列验证

4. **Renderer** (`src/modules/renderer.js`)
   - Canvas绘制功能
   - 进度显示、信息面板
   - 图像导出功能

5. **StringArtGenerator** (`src/modules/stringArtGenerator.js`)
   - 主控制器，协调所有模块
   - 提供统一的API接口
   - 状态管理和缓存

## 快速开始

### 基本使用

```javascript
import StringArtGenerator from './src/modules/stringArtGenerator.js';

// 创建生成器实例
const generator = new StringArtGenerator({
  imageSize: 500,
  numberOfPins: 288,
  maxLines: 4000,
  lineWeight: 20
});

// 处理图像
const canvas = document.createElement('canvas');
await generator.processImage(imageFile, canvas);

// 生成String Art
const result = await generator.generateStringArt({
  canvas: outputCanvas,
  onProgress: (progress) => {
    console.log(`Progress: ${Math.round(progress.progress * 100)}%`);
  }
});

console.log(`Generated ${result.totalLines} lines`);
```

### 在Next.js中使用

```jsx
import { useStringArtGenerator } from './examples/nextjs-usage.js';

function MyComponent() {
  const {
    generator,
    state,
    result,
    error,
    processImage,
    generateStringArt,
    reset
  } = useStringArtGenerator({
    imageSize: 500,
    numberOfPins: 288,
    maxLines: 4000
  });

  const handleFileSelect = async (file) => {
    await processImage(file, canvasRef.current);
  };

  const handleGenerate = async () => {
    await generateStringArt({
      canvas: outputCanvasRef.current,
      onProgress: setProgress
    });
  };

  // ... 组件渲染逻辑
}
```

## API 文档

### StringArtGenerator

#### 构造函数选项

```javascript
const generator = new StringArtGenerator({
  imageSize: 500,        // 图像尺寸
  numberOfPins: 288,     // 钉子数量
  maxLines: 4000,        // 最大线条数
  lineWeight: 20,        // 线条权重
  minDistance: 20,       // 最小距离
  minLoop: 20,           // 最小循环
  scale: 2,              // 渲染缩放
  hoopDiameter: 0.625    // 环直径
});
```

#### 主要方法

- `processImage(input, canvas)` - 处理图像
- `generateStringArt(options)` - 生成String Art
- `renderResult(canvas, lineSequence, options)` - 渲染结果
- `createInteractiveRenderer(canvas)` - 创建交互式渲染器
- `exportLineSequence()` - 导出线条序列
- `importLineSequence(sequenceString)` - 导入线条序列
- `getState()` - 获取当前状态
- `reset()` - 重置生成器

### 事件回调

#### onProgress 回调

```javascript
onProgress: (progressData) => {
  console.log(progressData.lineIndex);    // 当前线条索引
  console.log(progressData.totalLines);   // 总线条数
  console.log(progressData.progress);     // 进度 (0-1)
  console.log(progressData.currentPin);   // 当前钉子
  console.log(progressData.threadLength); // 线程长度
}
```

#### onLineDrawn 回调

```javascript
onLineDrawn: (lineData) => {
  console.log(lineData.fromPin);     // 起始钉子
  console.log(lineData.toPin);       // 结束钉子
  console.log(lineData.lineIndex);   // 线条索引
  console.log(lineData.coordinates); // 坐标数据
}
```

## 配置选项

### 图像处理配置

- `imageSize`: 处理后的图像尺寸（默认500）

### 几何配置

- `numberOfPins`: 圆周上的钉子数量（默认288）
- `minDistance`: 钉子之间的最小距离（默认20）
- `hoopDiameter`: 物理环的直径（默认0.625）

### 算法配置

- `maxLines`: 最大生成线条数（默认4000）
- `lineWeight`: 线条权重，影响算法选择（默认20）
- `minLoop`: 避免重复的最小循环长度（默认20）

### 渲染配置

- `scale`: 输出画布的缩放因子（默认2）
- `lineWidth`: 线条宽度（默认0.3）
- `backgroundColor`: 背景颜色（默认'#ffffff'）
- `lineColor`: 线条颜色（默认'#000000'）

## 高级功能

### 交互式渲染

```javascript
const interactive = generator.createInteractiveRenderer(canvas);

// 控制播放
interactive.nextStep();      // 下一步
interactive.previousStep();  // 上一步
interactive.goToStep(100);   // 跳转到指定步骤
interactive.reset();         // 重置
interactive.complete();      // 完成
```

### 数据导入导出

```javascript
// 导出线条序列
const sequence = generator.exportLineSequence();
localStorage.setItem('stringArtSequence', sequence);

// 导入线条序列
const savedSequence = localStorage.getItem('stringArtSequence');
generator.importLineSequence(savedSequence);

// 导出图像
generator.renderer.downloadImage(canvas, 'my-string-art', 'png');
```

### 状态监控

```javascript
const state = generator.getState();
console.log(state.isProcessing);        // 是否正在处理
console.log(state.currentStep);         // 当前步骤
console.log(state.progress);            // 进度
console.log(state.hasProcessedImage);   // 是否有处理过的图像
console.log(state.hasLineSequence);     // 是否有线条序列
```

## 性能优化建议

1. **图像尺寸**: 较小的图像尺寸可以显著提高处理速度
2. **钉子数量**: 减少钉子数量可以减少计算复杂度
3. **最大线条数**: 根据需要调整，更多线条意味着更长的处理时间
4. **异步处理**: 使用提供的进度回调来更新UI，避免阻塞

## 浏览器兼容性

- 现代浏览器（Chrome 60+, Firefox 55+, Safari 12+）
- 需要Canvas 2D API支持
- 需要ES6+ 支持（或使用Babel转译）

## 许可证

MIT License
