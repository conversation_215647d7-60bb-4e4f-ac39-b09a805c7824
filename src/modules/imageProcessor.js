/**
 * 图像处理模块
 * 负责图像的加载、裁剪、灰度化、圆形裁剪等功能
 */

export class ImageProcessor {
  constructor(options = {}) {
    this.imageSize = options.imageSize || 500;
  }

  /**
   * 从文件加载图像
   * @param {File} file - 图像文件
   * @returns {Promise<HTMLImageElement>} 加载的图像元素
   */
  async loadImageFromFile(file) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * 从URL加载图像
   * @param {string} url - 图像URL
   * @returns {Promise<HTMLImageElement>} 加载的图像元素
   */
  async loadImageFromUrl(url) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = url;
    });
  }

  /**
   * 将图像裁剪为正方形并调整大小
   * @param {HTMLImageElement} image - 源图像
   * @param {HTMLCanvasElement} canvas - 目标画布
   * @returns {ImageData} 处理后的图像数据
   */
  cropAndResizeImage(image, canvas) {
    const ctx = canvas.getContext('2d');
    canvas.width = this.imageSize;
    canvas.height = this.imageSize;
    ctx.clearRect(0, 0, this.imageSize, this.imageSize);

    // 计算裁剪参数以获得正方形
    let selectedWidth = image.width;
    let selectedHeight = image.height;
    let xOffset = 0;
    let yOffset = 0;

    if (image.height > image.width) {
      selectedWidth = image.width;
      selectedHeight = image.width;
      yOffset = Math.floor((image.height - image.width) / 2);
    } else if (image.width > image.height) {
      selectedWidth = image.height;
      selectedHeight = image.height;
      xOffset = Math.floor((image.width - image.height) / 2);
    }

    // 绘制裁剪后的图像
    ctx.drawImage(
      image,
      xOffset, yOffset, selectedWidth, selectedHeight,
      0, 0, this.imageSize, this.imageSize
    );

    return ctx.getImageData(0, 0, this.imageSize, this.imageSize);
  }

  /**
   * 将图像转换为灰度
   * @param {ImageData} imageData - 图像数据
   * @returns {Object} 包含灰度数据的对象
   */
  convertToGrayscale(imageData) {
    const grayscaleData = [];
    const pixels = imageData.data;

    for (let y = 0; y < imageData.height; y++) {
      for (let x = 0; x < imageData.width; x++) {
        const i = (y * 4) * imageData.width + x * 4;
        const avg = (pixels[i] + pixels[i + 1] + pixels[i + 2]) / 3;
        
        // 更新像素为灰度值
        pixels[i] = avg;     // R
        pixels[i + 1] = avg; // G
        pixels[i + 2] = avg; // B
        // pixels[i + 3] 保持不变 (Alpha)
        
        grayscaleData.push(avg);
      }
    }

    return {
      data: grayscaleData,
      width: imageData.width,
      height: imageData.height,
      imageData: imageData
    };
  }

  /**
   * 应用圆形遮罩
   * @param {HTMLCanvasElement} canvas - 画布
   * @param {ImageData} imageData - 图像数据
   */
  applyCircularMask(canvas, imageData) {
    const ctx = canvas.getContext('2d');
    
    // 先绘制图像数据
    ctx.putImageData(imageData, 0, 0);
    
    // 应用圆形遮罩
    ctx.globalCompositeOperation = 'destination-in';
    ctx.beginPath();
    ctx.arc(this.imageSize / 2, this.imageSize / 2, this.imageSize / 2, 0, Math.PI * 2);
    ctx.closePath();
    ctx.fill();
    
    // 重置合成操作
    ctx.globalCompositeOperation = 'source-over';
  }

  /**
   * 完整的图像处理流程
   * @param {HTMLImageElement|File|string} input - 图像输入（图像元素、文件或URL）
   * @param {HTMLCanvasElement} canvas - 目标画布
   * @returns {Promise<Object>} 处理结果
   */
  async processImage(input, canvas) {
    let image;

    // 根据输入类型加载图像
    if (input instanceof HTMLImageElement) {
      image = input;
    } else if (input instanceof File) {
      image = await this.loadImageFromFile(input);
    } else if (typeof input === 'string') {
      image = await this.loadImageFromUrl(input);
    } else {
      throw new Error('Unsupported input type');
    }

    // 裁剪和调整大小
    const imageData = this.cropAndResizeImage(image, canvas);
    
    // 转换为灰度
    const grayscaleResult = this.convertToGrayscale(imageData);
    
    // 应用圆形遮罩
    this.applyCircularMask(canvas, grayscaleResult.imageData);

    return {
      originalImage: image,
      grayscaleData: grayscaleResult.data,
      width: grayscaleResult.width,
      height: grayscaleResult.height,
      canvas: canvas
    };
  }

  /**
   * 创建错误图像数组（用于算法计算）
   * @param {Array} grayscaleData - 灰度数据
   * @param {number} size - 图像尺寸
   * @returns {Array} 错误数组
   */
  createErrorArray(grayscaleData, size) {
    const errorArray = [];
    for (let i = 0; i < grayscaleData.length; i++) {
      errorArray[i] = 255 - grayscaleData[i];
    }
    return errorArray;
  }

  /**
   * 获取指定位置的像素值
   * @param {Array} data - 图像数据数组
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 图像宽度
   * @returns {number} 像素值
   */
  getPixel(data, x, y, width) {
    if (x < 0 || y < 0 || x >= width || y >= width) {
      return 0;
    }
    return data[y * width + x];
  }

  /**
   * 设置指定位置的像素值
   * @param {Array} data - 图像数据数组
   * @param {number} x - X坐标
   * @param {number} y - Y坐标
   * @param {number} width - 图像宽度
   * @param {number} value - 像素值
   */
  setPixel(data, x, y, width, value) {
    if (x >= 0 && y >= 0 && x < width && y < width) {
      data[y * width + x] = value;
    }
  }
}

export default ImageProcessor;
