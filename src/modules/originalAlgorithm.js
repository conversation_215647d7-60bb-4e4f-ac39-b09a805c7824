/**
 * 原版本算法的完整实现
 * 与index.html中的算法完全一致
 */
class OriginalStringArtAlgorithm {
  constructor(options = {}) {
    // 默认参数（与原版本一致）
    this.IMG_SIZE = options.imageSize || 500;
    this.N_PINS = options.numberOfPins || 288;
    this.MAX_LINES = options.maxLines || 4000;
    this.LINE_WEIGHT = options.lineWeight || 20;
    this.MIN_DISTANCE = options.minDistance || 20;
    this.HOOP_DIAMETER = options.hoopDiameter || 0.625;
    this.SCALE = options.scale || 2;

    // 状态变量
    this.pin_coords = [];
    this.line_cache_x = null;
    this.line_cache_y = null;
    this.line_cache_length = null;
    this.line_cache_weight = null;
    this.error = null;
    this.img_result = null;
    this.line_sequence = [];
    this.thread_length = 0;
  }

  /**
   * 创建类似numjs的数组对象
   */
  createNjArray(shape, initialValue = 0) {
    const size = shape[0] * shape[1];
    const data = new Array(size).fill(initialValue);

    return {
      shape: shape,
      selection: {
        data: data
      },
      get: function (y, x) {
        return this.selection.data[y * this.shape[1] + x];
      },
      set: function (y, x, value) {
        this.selection.data[y * this.shape[1] + x] = value;
        return this;
      },
      multiply: function (value) {
        const newData = this.selection.data.map(v => v * value);
        const result = createNjArray(this.shape);
        result.selection.data = newData;
        return result;
      },
      subtract: function (other) {
        const newData = new Array(this.selection.data.length);
        for (let i = 0; i < this.selection.data.length; i++) {
          newData[i] = this.selection.data[i] - other.selection.data[i];
        }
        const result = createNjArray(this.shape);
        result.selection.data = newData;
        return result;
      }
    };
  }

  /**
   * 处理图像数据（与原版本一致）
   */
  processImageData(imageData) {
    const canvas = document.createElement('canvas');
    canvas.width = this.IMG_SIZE;
    canvas.height = this.IMG_SIZE;
    const ctx = canvas.getContext('2d');

    // 将ImageData绘制到canvas上
    ctx.putImageData(imageData, 0, 0);

    // 获取图像数据并转换为灰度
    const imgPixels = ctx.getImageData(0, 0, this.IMG_SIZE, this.IMG_SIZE);
    const R = this.createNjArray([this.IMG_SIZE, this.IMG_SIZE], 0xff);
    const rdata = [];

    for (let y = 0; y < imgPixels.height; y++) {
      for (let x = 0; x < imgPixels.width; x++) {
        const i = (y * 4) * imgPixels.width + x * 4;
        const avg = (imgPixels.data[i] + imgPixels.data[i + 1] + imgPixels.data[i + 2]) / 3;
        rdata.push(avg);
      }
    }

    R.selection.data = rdata;

    // 创建误差数组（与原版本一致）
    this.error = this.createNjArray([this.IMG_SIZE, this.IMG_SIZE], 0xff)
      .subtract(this.uint8Array(R.selection.data).reshape(this.IMG_SIZE, this.IMG_SIZE));

    this.img_result = this.createNjArray([this.IMG_SIZE, this.IMG_SIZE], 0xff);

    return R;
  }

  /**
   * 计算钉子位置（与原版本一致）
   */
  calculatePins() {
    this.pin_coords = [];
    const center = this.IMG_SIZE / 2;
    const radius = this.IMG_SIZE / 2 - 1 / 2;

    for (let i = 0; i < this.N_PINS; i++) {
      const angle = 2 * Math.PI * i / this.N_PINS;
      this.pin_coords.push([
        Math.floor(center + radius * Math.cos(angle)),
        Math.floor(center + radius * Math.sin(angle))
      ]);
    }

    return this.pin_coords;
  }

  /**
   * 预计算所有线条（与原版本一致）
   */
  precalculateLines() {
    this.line_cache_y = new Array(this.N_PINS * this.N_PINS);
    this.line_cache_x = new Array(this.N_PINS * this.N_PINS);
    this.line_cache_length = new Array(this.N_PINS * this.N_PINS).fill(0);
    this.line_cache_weight = new Array(this.N_PINS * this.N_PINS).fill(1);

    for (let a = 0; a < this.N_PINS; a++) {
      for (let b = a + this.MIN_DISTANCE; b < this.N_PINS; b++) {
        const x0 = this.pin_coords[a][0];
        const y0 = this.pin_coords[a][1];
        const x1 = this.pin_coords[b][0];
        const y1 = this.pin_coords[b][1];

        const d = Math.floor(Number(Math.sqrt((x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0))));
        const xs = this.linspace(x0, x1, d);
        const ys = this.linspace(y0, y1, d);

        this.line_cache_y[b * this.N_PINS + a] = ys;
        this.line_cache_y[a * this.N_PINS + b] = ys;
        this.line_cache_x[b * this.N_PINS + a] = xs;
        this.line_cache_x[a * this.N_PINS + b] = xs;
        this.line_cache_length[b * this.N_PINS + a] = d;
        this.line_cache_length[a * this.N_PINS + b] = d;
      }
    }
  }

  /**
   * linspace函数（与原版本完全一致）
   */
  linspace(a, b, n) {
    if (typeof n === "undefined") n = Math.max(Math.round(b - a) + 1, 1);
    if (n < 2) { return n === 1 ? [a] : []; }
    const ret = new Array(n);
    n--;
    for (let i = n; i >= 0; i--) {
      ret[i] = Math.floor((i * b + (n - i) * a) / n);
    }
    return ret;
  }

  /**
   * 获取线条误差（与原版本一致）
   */
  getLineErr(arr, coords1, coords2) {
    let result = new Uint8Array(coords1.length);
    for (let i = 0; i < coords1.length; i++) {
      result[i] = arr.get(coords1[i], coords2[i]);
    }
    return this.getSum(result);
  }

  /**
   * 设置线条（与原版本一致）
   */
  setLine(arr, coords1, coords2, line) {
    for (let i = 0; i < coords1.length; i++) {
      arr.set(coords1[i], coords2[i], line);
    }
    return arr;
  }

  /**
   * 数组求和
   */
  getSum(arr) {
    let v = 0;
    for (let i = 0; i < arr.length; i++) {
      v = v + arr[i];
    }
    return v;
  }

  /**
   * 数组相减（与原版本一致）
   */
  subtractArrays(arr1, arr2) {
    for (let i = 0; i < arr1.selection.data.length; i++) {
      arr1.selection.data[i] = arr1.selection.data[i] - arr2.selection.data[i];
      if (arr1.selection.data[i] < 0) {
        arr1.selection.data[i] = 0;
      } else if (arr1.selection.data[i] > 255) {
        arr1.selection.data[i] = 255;
      }
    }
    return arr1;
  }

  /**
   * 创建uint8数组
   */
  uint8Array(data) {
    return {
      reshape: (rows, cols) => {
        const result = this.createNjArray([rows, cols]);
        result.selection.data = [...data];
        return result;
      }
    };
  }

  /**
   * 创建零数组
   */
  zeros(shape, dtype = 'float64') {
    return this.createNjArray(shape, 0);
  }

  /**
   * 主要的线条计算算法（与原版本完全一致）
   */
  async calculateLines(onProgress = () => { }) {
    this.line_sequence = [];
    let pin = 0;
    this.line_sequence.push(pin);
    this.thread_length = 0;
    const last_pins = [];

    for (let l = 0; l < this.MAX_LINES; l++) {
      // 进度回调
      if (l % 10 === 0) {
        onProgress({
          step: 'drawing-lines',
          progress: l / this.MAX_LINES,
          linesDrawn: l,
          totalLines: this.MAX_LINES
        });

        // 让出控制权，避免阻塞UI
        await new Promise(resolve => setTimeout(resolve, 0));
      }

      let max_err = -1;
      let best_pin = -1;

      // 寻找最佳下一个钉子
      for (let offset = this.MIN_DISTANCE; offset < this.N_PINS - this.MIN_DISTANCE; offset++) {
        const test_pin = (pin + offset) % this.N_PINS;
        if (last_pins.includes(test_pin)) {
          continue;
        }

        const xs = this.line_cache_x[test_pin * this.N_PINS + pin];
        const ys = this.line_cache_y[test_pin * this.N_PINS + pin];

        const line_err = this.getLineErr(this.error, ys, xs) * this.line_cache_weight[test_pin * this.N_PINS + pin];

        if (line_err > max_err) {
          max_err = line_err;
          best_pin = test_pin;
        }
      }

      this.line_sequence.push(best_pin);

      // 更新误差数组
      const xs = this.line_cache_x[best_pin * this.N_PINS + pin];
      const ys = this.line_cache_y[best_pin * this.N_PINS + pin];
      const weight = this.LINE_WEIGHT * this.line_cache_weight[best_pin * this.N_PINS + pin];

      const line_mask = this.zeros([this.IMG_SIZE, this.IMG_SIZE], 'float64');
      this.setLine(line_mask, ys, xs, weight);
      this.error = this.subtractArrays(this.error, line_mask);

      // 计算线程长度
      const x0 = this.pin_coords[pin][0];
      const y0 = this.pin_coords[pin][1];
      const x1 = this.pin_coords[best_pin][0];
      const y1 = this.pin_coords[best_pin][1];

      const dist = Math.sqrt((x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0));
      this.thread_length += this.HOOP_DIAMETER / this.IMG_SIZE * dist;

      // 更新最近使用的钉子列表
      last_pins.push(best_pin);
      if (last_pins.length > 20) {
        last_pins.shift();
      }
      pin = best_pin;
    }

    // 最终进度回调
    onProgress({
      step: 'complete',
      progress: 1,
      linesDrawn: this.MAX_LINES,
      totalLines: this.MAX_LINES
    });

    return {
      lineSequence: this.line_sequence,
      threadLength: this.thread_length,
      pinCoords: this.pin_coords
    };
  }

  /**
   * 完整的生成流程
   */
  async generate(imageData, onProgress = () => { }) {
    try {
      // 1. 处理图像
      onProgress({ step: 'processing-image', progress: 0 });
      this.processImageData(imageData);

      // 2. 计算钉子位置
      onProgress({ step: 'calculating-pins', progress: 0 });
      this.calculatePins();

      // 3. 预计算线条
      onProgress({ step: 'precalculating-lines', progress: 0 });
      this.precalculateLines();

      // 4. 生成线条序列
      const result = await this.calculateLines(onProgress);

      return result;
    } catch (error) {
      throw new Error(`String art generation failed: ${error.message}`);
    }
  }
}

// 全局函数，用于创建数组
function createNjArray(shape, initialValue = 0) {
  const size = shape[0] * shape[1];
  const data = new Array(size).fill(initialValue);

  return {
    shape: shape,
    selection: {
      data: data
    },
    get: function (y, x) {
      return this.selection.data[y * this.shape[1] + x];
    },
    set: function (y, x, value) {
      this.selection.data[y * this.shape[1] + x] = value;
      return this;
    },
    multiply: function (value) {
      const newData = this.selection.data.map(v => v * value);
      const result = createNjArray(this.shape);
      result.selection.data = newData;
      return result;
    },
    subtract: function (other) {
      const newData = new Array(this.selection.data.length);
      for (let i = 0; i < this.selection.data.length; i++) {
        newData[i] = this.selection.data[i] - other.selection.data[i];
      }
      const result = createNjArray(this.shape);
      result.selection.data = newData;
      return result;
    }
  };
}

export default OriginalStringArtAlgorithm;
