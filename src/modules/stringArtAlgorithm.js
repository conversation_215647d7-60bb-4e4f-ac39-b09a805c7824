/**
 * String Art算法核心模块
 * 负责生成弦线艺术的核心算法逻辑
 */

export class StringArtAlgorithm {
  constructor(options = {}) {
    this.maxLines = options.maxLines || 4000;
    this.lineWeight = options.lineWeight || 20;
    this.minLoop = options.minLoop || 20;
    this.imageSize = options.imageSize || 500;
  }

  /**
   * 计算线条在图像上的误差贡献
   * @param {Array} errorArray - 误差数组 (一维数组，按行存储)
   * @param {Array} yCoords - Y坐标数组
   * @param {Array} xCoords - X坐标数组
   * @returns {number} 线条误差值
   */
  calculateLineError(errorArray, yCoords, xCoords) {
    let totalError = 0;

    for (let i = 0; i < yCoords.length; i++) {
      const y = Math.floor(yCoords[i]);
      const x = Math.floor(xCoords[i]);

      if (y >= 0 && y < this.imageSize && x >= 0 && x < this.imageSize) {
        const index = y * this.imageSize + x;
        totalError += errorArray[index];
      }
    }

    return totalError;
  }

  /**
   * 在误差数组上绘制线条（减去线条的贡献）
   * @param {Array} errorArray - 误差数组 (一维数组，按行存储)
   * @param {Array} yCoords - Y坐标数组
   * @param {Array} xCoords - X坐标数组
   * @param {number} weight - 线条权重
   * @returns {Array} 更新后的误差数组
   */
  drawLineOnError(errorArray, yCoords, xCoords, weight) {
    for (let i = 0; i < yCoords.length; i++) {
      const y = Math.floor(yCoords[i]);
      const x = Math.floor(xCoords[i]);

      if (y >= 0 && y < this.imageSize && x >= 0 && x < this.imageSize) {
        const index = y * this.imageSize + x;
        errorArray[index] = Math.max(0, errorArray[index] - weight);
      }
    }

    return errorArray;
  }

  /**
   * 寻找下一条最佳线条
   * @param {number} currentPin - 当前钉子索引
   * @param {Array} errorArray - 误差数组
   * @param {Object} lineCache - 预计算的线条缓存
   * @param {Array} recentPins - 最近使用的钉子列表
   * @param {number} numberOfPins - 钉子总数
   * @param {number} minDistance - 最小距离
   * @returns {Object} 最佳线条信息
   */
  findBestLine(currentPin, errorArray, lineCache, recentPins, numberOfPins, minDistance) {
    let maxError = -1;
    let bestPin = -1;
    let bestLineError = 0;

    for (let offset = minDistance; offset < numberOfPins - minDistance; offset++) {
      const testPin = (currentPin + offset) % numberOfPins;

      // 跳过最近使用的钉子
      if (recentPins.includes(testPin)) {
        continue;
      }

      const lineIndex = testPin * numberOfPins + currentPin;
      const xCoords = lineCache.x[lineIndex];
      const yCoords = lineCache.y[lineIndex];

      if (xCoords && yCoords) {
        const lineError = this.calculateLineError(errorArray, yCoords, xCoords) * lineCache.weight[lineIndex];

        if (lineError > maxError) {
          maxError = lineError;
          bestPin = testPin;
          bestLineError = lineError;
        }
      }
    }

    return {
      pin: bestPin,
      error: bestLineError,
      maxError: maxError
    };
  }

  /**
   * 生成String Art线条序列
   * @param {Array} grayscaleData - 灰度图像数据
   * @param {Object} lineCache - 预计算的线条缓存
   * @param {Array} pinCoords - 钉子坐标
   * @param {Object} options - 生成选项
   * @returns {Promise<Object>} 生成结果
   */
  async generateStringArt(grayscaleData, lineCache, pinCoords, options = {}) {
    const {
      onProgress = () => { },
      onLineDrawn = () => { },
      numberOfPins = 288,
      minDistance = 20
    } = options;

    // 初始化误差数组
    const errorArray = grayscaleData.map(pixel => 255 - pixel);

    // 初始化结果
    const lineSequence = [];
    let currentPin = 0;
    lineSequence.push(currentPin);

    let totalThreadLength = 0;
    const recentPins = [];

    // 生成线条序列
    for (let lineIndex = 0; lineIndex < this.maxLines; lineIndex++) {
      // 寻找最佳下一条线
      const bestLine = this.findBestLine(
        currentPin,
        errorArray,
        lineCache,
        recentPins,
        numberOfPins,
        minDistance
      );

      if (bestLine.pin === -1) {
        console.warn(`No valid line found at step ${lineIndex}`);
        break;
      }

      // 添加到序列
      lineSequence.push(bestLine.pin);

      // 获取线条坐标
      const lineIndexCache = bestLine.pin * numberOfPins + currentPin;
      const xCoords = lineCache.x[lineIndexCache];
      const yCoords = lineCache.y[lineIndexCache];
      const weight = this.lineWeight * lineCache.weight[lineIndexCache];

      // 在误差数组上绘制线条
      this.drawLineOnError(errorArray, yCoords, xCoords, weight);

      // 计算物理长度
      const [x0, y0] = pinCoords[currentPin];
      const [x1, y1] = pinCoords[bestLine.pin];
      const distance = Math.sqrt((x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0));
      totalThreadLength += distance;

      // 更新最近使用的钉子列表
      recentPins.push(bestLine.pin);
      if (recentPins.length > this.minLoop) {
        recentPins.shift();
      }

      // 更新当前钉子
      currentPin = bestLine.pin;

      // 调用进度回调
      onProgress({
        lineIndex: lineIndex + 1,
        totalLines: this.maxLines,
        progress: (lineIndex + 1) / this.maxLines,
        currentPin: currentPin,
        threadLength: totalThreadLength,
        error: bestLine.error
      });

      // 调用线条绘制回调
      onLineDrawn({
        fromPin: lineSequence[lineSequence.length - 2],
        toPin: currentPin,
        lineIndex: lineIndex + 1,
        coordinates: { xCoords, yCoords }
      });

      // 允许异步中断
      if (lineIndex % 10 === 0) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }

    return {
      lineSequence,
      totalLines: lineSequence.length - 1,
      threadLength: totalThreadLength,
      errorArray: errorArray
    };
  }

  /**
   * 验证线条序列的有效性
   * @param {Array} lineSequence - 线条序列
   * @param {number} numberOfPins - 钉子总数
   * @param {number} minDistance - 最小距离
   * @returns {Object} 验证结果
   */
  validateLineSequence(lineSequence, numberOfPins, minDistance) {
    const errors = [];
    const warnings = [];

    for (let i = 1; i < lineSequence.length; i++) {
      const prevPin = lineSequence[i - 1];
      const currentPin = lineSequence[i];

      // 检查钉子索引是否有效
      if (prevPin < 0 || prevPin >= numberOfPins || currentPin < 0 || currentPin >= numberOfPins) {
        errors.push(`Invalid pin index at step ${i}: ${prevPin} -> ${currentPin}`);
        continue;
      }

      // 检查最小距离
      const distance = Math.abs(currentPin - prevPin);
      const circularDistance = Math.min(distance, numberOfPins - distance);

      if (circularDistance < minDistance) {
        warnings.push(`Distance too small at step ${i}: ${circularDistance} < ${minDistance}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      totalLines: lineSequence.length - 1
    };
  }

  /**
   * 计算线条序列的统计信息
   * @param {Array} lineSequence - 线条序列
   * @param {Array} pinCoords - 钉子坐标
   * @returns {Object} 统计信息
   */
  calculateStatistics(lineSequence, pinCoords) {
    let totalLength = 0;
    const pinUsage = new Array(pinCoords.length).fill(0);
    const distances = [];

    for (let i = 1; i < lineSequence.length; i++) {
      const prevPin = lineSequence[i - 1];
      const currentPin = lineSequence[i];

      // 统计钉子使用次数
      pinUsage[prevPin]++;
      pinUsage[currentPin]++;

      // 计算距离
      const [x0, y0] = pinCoords[prevPin];
      const [x1, y1] = pinCoords[currentPin];
      const distance = Math.sqrt((x1 - x0) * (x1 - x0) + (y1 - y0) * (y1 - y0));

      totalLength += distance;
      distances.push(distance);
    }

    return {
      totalLength,
      averageLength: totalLength / distances.length,
      minLength: Math.min(...distances),
      maxLength: Math.max(...distances),
      pinUsage,
      mostUsedPin: pinUsage.indexOf(Math.max(...pinUsage)),
      leastUsedPin: pinUsage.indexOf(Math.min(...pinUsage)),
      totalLines: lineSequence.length - 1
    };
  }
}

export default StringArtAlgorithm;
